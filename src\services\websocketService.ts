/**
 * Modern WebSocket service for real-time communication
 * Handles WebSocket connections, message routing, and reconnection logic
 */

import { WEBSOCKET_URL, WEBSOCKET_RECONNECT_DELAY, WEBSOCKET_MAX_RECONNECT_ATTEMPTS } from '@/constants';

// WebSocket message types
export interface WebSocketMessage {
  type: string;
  content: any;
  metadata?: any;
  timestamp: string;
  task_id?: string;
  user_id?: string;
}

export interface WebSocketCommand {
  action: string;
  task_id?: string;
  message?: string;
  data?: any;
}

export interface WebSocketResponse {
  success: boolean;
  message: string;
  data?: any;
  error_code?: string;
}

// Event handler types
export type MessageHandler = (message: WebSocketMessage) => void;
export type ErrorHandler = (error: Error) => void;
export type ConnectionHandler = () => void;

class WebSocketService {
  private ws: WebSocket | null = null;
  private token: string | null = null;
  private reconnectAttempts = 0;
  private isManualClose = false;
  private connectionPromise: Promise<void> | null = null;

  // Event handlers
  private messageHandlers: Set<MessageHandler> = new Set();
  private errorHandlers: Set<ErrorHandler> = new Set();
  private connectHandlers: Set<ConnectionHandler> = new Set();
  private disconnectHandlers: Set<ConnectionHandler> = new Set();

  // Heartbeat
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private lastHeartbeat: number = 0;

  /**
   * Connect to WebSocket server
   */
  async connect(token: string): Promise<void> {
    // Return existing connection promise if one is in progress
    if (this.connectionPromise) {
      console.log('🔗 WebSocket connection already in progress, waiting...');
      return this.connectionPromise;
    }

    if (this.isConnected()) {
      console.log('🔗 WebSocket already connected');
      return;
    }

    // Create and store the connection promise
    this.connectionPromise = this.performConnection(token);

    try {
      await this.connectionPromise;
    } finally {
      this.connectionPromise = null;
    }
  }

  /**
   * Perform the actual connection
   */
  private async performConnection(token: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.token = token;
        this.isManualClose = false;

        // Create WebSocket URL with token
        const wsUrl = `${WEBSOCKET_URL}?token=${encodeURIComponent(token)}`;
        console.log('🔗 Connecting to WebSocket:', wsUrl.replace(token, 'TOKEN_HIDDEN'));

        this.ws = new WebSocket(wsUrl);

        // Connection opened
        this.ws.onopen = () => {
          console.log('🔗 WebSocket connected successfully');
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyConnectHandlers();
          resolve();
        };

        // Message received
        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            this.handleMessage(message);
          } catch (error) {
            console.error('🔗 Error parsing WebSocket message:', error);
            this.notifyErrorHandlers(new Error('Failed to parse WebSocket message'));
          }
        };

        // Connection closed
        this.ws.onclose = (event) => {
          console.log('🔗 WebSocket connection closed:', event.code, event.reason);
          this.stopHeartbeat();
          this.notifyDisconnectHandlers();

          if (!this.isManualClose && this.shouldReconnect()) {
            this.scheduleReconnect();
          }
        };

        // Connection error
        this.ws.onerror = (error) => {
          console.error('🔗 WebSocket error:', error);
          // Don't send error messages to chat - just handle connection state
          reject(new Error('WebSocket connection failed'));
        };

        // Connection timeout
        setTimeout(() => {
          if (this.ws?.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            reject(new Error('WebSocket connection timeout'));
          }
        }, 10000);

      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  disconnect(): void {
    this.isManualClose = true;
    this.stopHeartbeat();
    
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect');
      this.ws = null;
    }
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection state
   */
  getConnectionState(): string {
    if (!this.ws) return 'DISCONNECTED';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING';
      case WebSocket.OPEN:
        return 'CONNECTED';
      case WebSocket.CLOSING:
        return 'CLOSING';
      case WebSocket.CLOSED:
        return 'DISCONNECTED';
      default:
        return 'UNKNOWN';
    }
  }

  /**
   * Send command to server
   */
  sendCommand(command: WebSocketCommand): void {
    if (!this.isConnected()) {
      throw new Error('WebSocket is not connected');
    }

    this.ws!.send(JSON.stringify(command));
  }

  /**
   * Send task message
   */
  sendTaskMessage(taskId: string, message: string): void {
    this.sendCommand({
      action: 'send_message',
      task_id: taskId,
      message: message
    });
  }

  /**
   * Subscribe to task updates
   */
  subscribeToTask(taskId: string): void {
    this.sendCommand({
      action: 'subscribe_task',
      task_id: taskId
    });
  }

  /**
   * Unsubscribe from task updates
   */
  unsubscribeFromTask(taskId: string): void {
    this.sendCommand({
      action: 'unsubscribe_task',
      task_id: taskId
    });
  }

  /**
   * Event handler management
   */
  onMessage(handler: MessageHandler): () => void {
    this.messageHandlers.add(handler);
    return () => this.messageHandlers.delete(handler);
  }

  onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler);
    return () => this.errorHandlers.delete(handler);
  }

  onConnect(handler: ConnectionHandler): () => void {
    this.connectHandlers.add(handler);
    return () => this.connectHandlers.delete(handler);
  }

  onDisconnect(handler: ConnectionHandler): () => void {
    this.disconnectHandlers.add(handler);
    return () => this.disconnectHandlers.delete(handler);
  }

  /**
   * Handle incoming message
   */
  private handleMessage(message: WebSocketMessage): void {
    // Update heartbeat timestamp for heartbeat messages
    if (message.type === 'heartbeat') {
      this.lastHeartbeat = Date.now();
      return; // Don't forward heartbeat messages to handlers
    }

    // Filter out system messages that shouldn't be displayed
    if (message.type === 'info' && typeof message.content === 'object' && message.content !== null) {
      // Check if this looks like a WebSocketResponse object
      if ('success' in message.content && 'message' in message.content) {
        console.log('🔗 Filtering out WebSocketResponse object:', message.content);
        return; // Don't forward raw WebSocketResponse objects
      }
    }

    // Notify all message handlers
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('🔗 Error in message handler:', error);
      }
    });
  }

  /**
   * Notify handlers
   */
  private notifyErrorHandlers(error: Error): void {
    this.errorHandlers.forEach(handler => {
      try {
        handler(error);
      } catch (err) {
        console.error('🔗 Error in error handler:', err);
      }
    });
  }

  private notifyConnectHandlers(): void {
    this.connectHandlers.forEach(handler => {
      try {
        handler();
      } catch (error) {
        console.error('🔗 Error in connect handler:', error);
      }
    });
  }

  private notifyDisconnectHandlers(): void {
    this.disconnectHandlers.forEach(handler => {
      try {
        handler();
      } catch (error) {
        console.error('🔗 Error in disconnect handler:', error);
      }
    });
  }

  /**
   * Reconnection logic
   */
  private shouldReconnect(): boolean {
    return this.reconnectAttempts < WEBSOCKET_MAX_RECONNECT_ATTEMPTS;
  }

  private scheduleReconnect(): void {
    if (!this.token) return;

    this.reconnectAttempts++;
    const delay = WEBSOCKET_RECONNECT_DELAY * this.reconnectAttempts;

    console.log(`🔗 Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);

    setTimeout(() => {
      if (!this.isManualClose && this.token) {
        console.log(`🔗 Attempting reconnect ${this.reconnectAttempts}/${WEBSOCKET_MAX_RECONNECT_ATTEMPTS}`);
        this.connect(this.token).catch(error => {
          console.error('🔗 Reconnect failed:', error);
        });
      }
    }, delay);
  }

  /**
   * Heartbeat management
   */
  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.lastHeartbeat = Date.now();
    
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.sendHeartbeat();
      }
    }, 30000); // Send heartbeat every 30 seconds
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private sendHeartbeat(): void {
    try {
      this.sendCommand({ action: 'heartbeat' });
    } catch (error) {
      console.error('🔗 Failed to send heartbeat:', error);
    }
  }
}

// Export singleton instance
export const websocketService = new WebSocketService();
export default websocketService;
