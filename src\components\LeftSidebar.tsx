/**
 * LeftSidebar component with tabbed interface for servers, task history, and settings
 */

import React, { useState } from 'react';
import { <PERSON>, Spark<PERSON>, Server, History, Settings, Moon, Sun, LogOut, User, Trash2, CreditCard, Coins, Wifi } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { LeftSidebarProps } from '@/types';
import ServerPanel from './ServerPanel';
import TaskHistoryPanel from './TaskHistoryPanel';
import ConfirmationModal from './ConfirmationModal';
import { useTheme } from '@/hooks/useTheme';
import { useAuth } from '@/components/AuthProvider';

type TabType = 'servers' | 'history' | 'settings';

const LeftSidebar: React.FC<LeftSidebarProps> = ({
  isOpen,
  onToggle,
  servers,
  taskHistory,
  currentServerId,
  selectedTaskId,
  onServerSelect,
  onAddServer,
  onNewTask,
  onTaskSelect,
  onDeleteServer,
  onDeleteTask,
  onDeleteAllTasks,
  onDeleteAccount,
  executionStats,
  currentTask,
  messages,
  autoScroll,
  onExportHistory,
  onToggleAutoScroll,
  onLogoutRequest,
  wsConnected = true
}) => {
  const [activeTab, setActiveTab] = useState<TabType>('servers');
  const [deleteAllTasksModalOpen, setDeleteAllTasksModalOpen] = useState(false);
  const [deleteAccountModalOpen, setDeleteAccountModalOpen] = useState(false);
  const { toggleTheme, getThemeIcon, getThemeLabel } = useTheme();
  const { user } = useAuth();

  // Mock credits data - replace with real data from backend
  const [credits] = useState({ current: 150, total_purchased: 500, last_updated: new Date() });
  const [creditsToBuy, setCreditsToBuy] = useState(100);

  const getIcon = () => {
    const iconName = getThemeIcon();
    switch (iconName) {
      case 'Sun':
        return <Sun size={14} />;
      case 'Moon':
        return <Moon size={14} />;
      default:
        return <Sun size={14} />;
    }
  };
  return (
    <>
      {/* Mobile Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onToggle}
            className="lg:hidden fixed inset-0 bg-black/50 z-40"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: isOpen ? 0 : '-100%',
        }}
        transition={{
          type: 'spring',
          stiffness: 300,
          damping: 30,
        }}
        className={`
          fixed lg:relative
          top-0 left-0
          h-full lg:h-auto
          left-sidebar-mobile md:left-sidebar-tablet lg:w-80
          bg-theme-primary
          border-r border-theme-primary
          z-50 lg:z-auto
          flex flex-col
          lg:flex
          minimal-sidebar
          ${isOpen ? 'flex' : 'hidden lg:flex'}
        `}
      >
        {/* Sidebar Header with Logo */}
        <div className="p-3 border-b border-theme-primary flex items-center justify-between">
          <div className="flex items-center gap-3">
            <motion.div
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <Sparkles size={20} className="text-accent-primary" />
            </motion.div>
            <div className="flex flex-col">
              <h2 className="text-lg font-semibold text-theme-primary">VPS Admin</h2>
              <span className="text-xs text-theme-tertiary">AI-Powered Server Management</span>
            </div>
          </div>
          <button
            onClick={onToggle}
            className="lg:hidden p-2 text-theme-tertiary hover:text-theme-primary transition-colors rounded-lg hover:bg-surface-secondary"
          >
            <X size={18} />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-theme-primary bg-surface-primary">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setActiveTab('servers');
            }}
            className={`flex-1 p-3 flex items-center justify-center gap-2 transition-colors ${activeTab === 'servers'
                ? 'text-theme-primary border-b-2 border-accent-primary bg-surface-secondary'
                : 'text-theme-tertiary hover:text-theme-secondary hover:bg-surface-secondary'
              }`}
            title="Servers"
          >
            <Wifi size={16} />
            <span className="text-sm font-medium">Servers</span>
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setActiveTab('history');
            }}
            className={`flex-1 p-3 flex items-center justify-center gap-2 transition-colors ${activeTab === 'history'
                ? 'text-theme-primary border-b-2 border-accent-primary bg-surface-secondary'
                : 'text-theme-tertiary hover:text-theme-secondary hover:bg-surface-secondary'
              }`}
            title="Task History"
          >
            <History size={16} />
            <span className="text-sm font-medium">History</span>
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setActiveTab('settings');
            }}
            className={`flex-1 p-3 flex items-center justify-center gap-2 transition-colors ${activeTab === 'settings'
                ? 'text-theme-primary border-b-2 border-accent-primary bg-surface-secondary'
                : 'text-theme-tertiary hover:text-theme-secondary hover:bg-surface-secondary'
              }`}
            title="Settings"
          >
            <Settings size={16} />
            <span className="text-sm font-medium">Settings</span>
          </button>
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden p-3 sidebar-content minimal-scrollbar">
          {activeTab === 'servers' && (
            <ServerPanel
              servers={servers}
              currentServerId={currentServerId}
              onServerSelect={onServerSelect}
              onAddServer={onAddServer}
              onDeleteServer={onDeleteServer}
            />
          )}

          {activeTab === 'history' && (
            <TaskHistoryPanel
              tasks={taskHistory}
              selectedTaskId={selectedTaskId}
              onTaskSelect={onTaskSelect}
              onNewTask={onNewTask}
              onDeleteTask={onDeleteTask}
            />
          )}

          {activeTab === 'settings' && (
            <div className="flex flex-col h-full">
              {/* Panel Header */}
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-theme-primary flex items-center gap-2">
                  <Settings size={16} className="text-accent-primary" />
                  Settings
                </h3>
              </div>

              {/* Settings Content - Scrollable */}
              <div className="flex-1 overflow-y-auto overflow-x-hidden minimal-scrollbar">
                <div className="space-y-4">
                  {/* Profile Section */}
                  <div className="bg-surface-primary p-3 rounded-lg border border-theme-primary">
                    <h4 className="font-medium text-sm text-theme-secondary mb-2">Profile</h4>
                    {user && (
                      <div className="flex items-center gap-3 p-2 bg-surface-secondary rounded-lg border border-theme-primary">
                        <div className="flex items-center justify-center w-8 h-8 bg-accent-primary rounded-full">
                          <User size={14} className="text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium text-theme-primary">{user.user_id}</div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Credits System */}
                  <div className="bg-surface-primary p-3 rounded-lg border border-theme-primary">
                    <h4 className="font-medium text-sm text-theme-secondary mb-2 flex items-center gap-2">
                      <Coins size={14} className="text-accent-primary" />
                      Credits
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-theme-tertiary">Available:</span>
                        <span className="text-sm font-mono text-accent-primary font-semibold">{credits.current}</span>
                      </div>

                      {/* Buy Credits Section */}
                      <div className="space-y-2">
                        <div className="flex items-stretch gap-2">
                          <div className="flex flex-1 bg-surface-secondary border border-theme-primary rounded-lg overflow-hidden focus-within:ring-2 focus-within:ring-accent-primary focus-within:border-transparent transition-all duration-200">
                            <div className="flex items-center px-3 text-xs text-theme-tertiary bg-theme-secondary border-r border-theme-primary whitespace-nowrap">
                              Amount
                            </div>
                            <input
                              type="number"
                              min="1"
                              value={creditsToBuy}
                              onChange={(e) => setCreditsToBuy(Math.max(1, parseInt(e.target.value) || 1))}
                              onClick={(e) => e.stopPropagation()}
                              onFocus={(e) => e.stopPropagation()}
                              className="flex-1 px-3 py-2 text-sm text-right bg-transparent text-theme-primary focus:outline-none appearance-none [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none min-w-0"
                              placeholder="100"
                            />
                          </div>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              // TODO: Handle credit purchase
                            }}
                            className="px-3 py-2 text-xs bg-accent-primary text-white rounded-lg hover:opacity-90 transition-all duration-200 flex items-center gap-1 whitespace-nowrap"
                          >
                            <CreditCard size={12} />
                            Buy Credits
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="bg-surface-primary p-3 rounded-lg border border-theme-primary">
                    <h4 className="font-medium text-sm text-theme-secondary mb-2">Quick Actions</h4>
                    <div className="space-y-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onExportHistory();
                        }}
                        className="w-full text-xs bg-accent-primary text-white px-3 py-2 rounded-lg hover:opacity-90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={taskHistory.length === 0 && messages.length === 0}
                      >
                        Export History
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onToggleAutoScroll();
                        }}
                        className={`w-full text-xs px-3 py-2 rounded-lg transition-all duration-200 ${autoScroll
                            ? 'bg-accent-secondary text-white hover:opacity-90'
                            : 'bg-surface-secondary text-theme-primary border border-theme-primary hover:bg-theme-secondary'
                          }`}
                      >
                        {autoScroll ? 'Disable' : 'Enable'} Auto-scroll
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setDeleteAllTasksModalOpen(true);
                        }}
                        className="w-full text-xs bg-accent-error text-white px-3 py-2 rounded-lg hover:opacity-90 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-1"
                        disabled={taskHistory.length === 0}
                      >
                        <Trash2 size={12} />
                        Delete All Tasks
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Delete Account Button - Sticky Bottom for Settings Tab */}
              <div className="mt-3 pt-3 border-t border-theme-primary">
                <motion.button
                  whileHover={{ scale: 1.01 }}
                  whileTap={{ scale: 0.99 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    setDeleteAccountModalOpen(true);
                  }}
                  className="w-full p-3 border border-dashed border-theme-primary hover:border-accent-primary rounded-lg transition-all duration-200 group hover:bg-surface-secondary"
                >
                  <div className="flex items-center justify-center gap-2 text-theme-tertiary group-hover:text-accent-primary">
                    <Trash2 size={16} />
                    <span className="text-sm font-medium">Delete Account</span>
                  </div>
                </motion.button>
              </div>
            </div>
          )}
        </div>

        {/* Sticky Bottom Section - Always Visible */}
        <div className="flex-shrink-0 p-3 border-t border-theme-primary bg-theme-primary">
          <div className="flex items-stretch gap-2">
            {/* System Status */}
            <div className="bg-surface-primary p-2 rounded-lg border border-theme-primary flex-1 flex items-center">
              <div className="flex items-center justify-between w-full">
                <span className="font-medium text-xs text-theme-secondary">Status</span>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-accent-secondary' : 'bg-accent-error'}`}></div>
                  <span className={`font-medium text-xs ${wsConnected ? 'text-accent-secondary' : 'text-accent-error'}`}>
                    {wsConnected ? 'Active' : 'Disconnected'}
                  </span>
                </div>
              </div>
            </div>

            {/* Controls */}
            <div className="bg-surface-primary p-2 rounded-lg border border-theme-primary flex items-center">
              <div className="flex items-center gap-1">
                {/* Theme Toggle Button */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleTheme();
                  }}
                  className="p-1.5 rounded-lg bg-transparent text-theme-secondary hover:bg-theme-tertiary transition-colors"
                  title={`Theme: ${getThemeLabel()}`}
                >
                  {getIcon()}
                </motion.button>

                {/* Logout Button */}
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onLogoutRequest();
                  }}
                  className="p-1.5 rounded-lg bg-transparent text-theme-secondary hover:bg-theme-tertiary transition-colors"
                  title="Logout"
                >
                  <LogOut size={14} />
                </motion.button>
              </div>
            </div>
          </div>
        </div>

      </motion.div>

      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={deleteAllTasksModalOpen}
        onClose={() => setDeleteAllTasksModalOpen(false)}
        onConfirm={() => {
          onDeleteAllTasks();
          setDeleteAllTasksModalOpen(false);
        }}
        title="Delete All Tasks"
        message={`Are you sure you want to delete all ${taskHistory.length} tasks? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        confirmButtonStyle="danger"
      />

      <ConfirmationModal
        isOpen={deleteAccountModalOpen}
        onClose={() => setDeleteAccountModalOpen(false)}
        onConfirm={() => {
          onDeleteAccount();
          setDeleteAccountModalOpen(false);
        }}
        title="Delete Account"
        message="Are you sure you want to delete your account? This will permanently remove all your data and cannot be undone."
        confirmText="Delete Account"
        cancelText="Cancel"
        confirmButtonStyle="danger"
      />
    </>
  );
};

export default LeftSidebar;
