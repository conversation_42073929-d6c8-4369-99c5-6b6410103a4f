"""
Enhanced Pydantic models for the WebSocket-based VPS Admin backend.
Includes models for authentication, tasks, WebSocket communication, and multi-tenancy.
"""

from datetime import datetime
from typing import Dict, List, Any, Optional
from enum import Enum
from pydantic import BaseModel, Field, field_validator

# Enums for better type safety
class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class MessageType(str, Enum):
    INFO = "info"
    ERROR = "error"
    SUCCESS = "success"
    WARNING = "warning"
    AI_RESPONSE = "ai_response"
    COMMAND_OUTPUT = "command_output"
    SSH_OUTPUT = "ssh_output"  # For SSH command output
    TASK_PLAN = "task_plan"
    FORM_REQUEST = "form_request"
    CONFIRMATION_REQUEST = "confirmation_request"
    COMMAND_CONFIRMATION = "command_confirmation"  # For regular command confirmation
    INTERACTIVE_COMMAND_CONFIRMATION = "interactive_command_confirmation"  # For interactive commands
    PROGRESS_UPDATE = "progress_update"
    TASK_COMPLETE = "task_complete"
    TASK_END = "task_end"  # For task completion
    STEP_SUCCESS = "step_success"  # For step completion
    QUESTION = "question"  # For questions
    HEARTBEAT = "heartbeat"

class DangerLevel(str, Enum):
    SAFE = "safe"
    CAUTION = "caution"
    DANGEROUS = "dangerous"

# Authentication models
class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    email: str = Field(..., pattern=r'^[^@]+@[^@]+\.[^@]+$')
    password: str = Field(..., min_length=8)

class UserLogin(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int
    user_id: str

class User(BaseModel):
    id: str
    username: str
    email: str
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None

# Task models
class TaskStep(BaseModel):
    description: str
    command: str
    rollback_command: Optional[str] = None
    danger_level: DangerLevel = DangerLevel.SAFE
    interactive: bool = False
    timeout: Optional[int] = None

class TaskPlan(BaseModel):
    task_name: str
    description: str
    steps: List[TaskStep]
    conclusion: str
    total_steps: int = Field(default=0)
    
    @field_validator('total_steps', mode='before')
    @classmethod
    def set_total_steps(cls, v, info):
        if hasattr(info, 'data') and 'steps' in info.data:
            return len(info.data['steps'])
        return v if v is not None else 0

class CreateTaskRequest(BaseModel):
    initial_prompt: str = Field(..., min_length=1, max_length=1000)
    priority: int = Field(default=1, ge=1, le=5)
    timeout: Optional[int] = Field(default=3600, ge=60, le=7200)

class CreateTaskResponse(BaseModel):
    task_id: str
    status: TaskStatus
    created_at: datetime

class Task(BaseModel):
    id: str
    user_id: str
    initial_prompt: str
    status: TaskStatus
    task_plan: Optional[TaskPlan] = None
    current_step_index: int = 0
    placeholder_values: Dict[str, str] = Field(default_factory=dict)
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    priority: int = 1
    timeout: int = 3600

# WebSocket models
class WebSocketMessage(BaseModel):
    type: MessageType
    content: Any = None
    metadata: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    task_id: Optional[str] = None
    user_id: Optional[str] = None

class WebSocketCommand(BaseModel):
    action: str
    task_id: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

class WebSocketResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    error_code: Optional[str] = None

# Form models
class FormField(BaseModel):
    name: str
    label: Optional[str] = None
    type: str = "text"
    required: bool = True
    default_value: str = ""
    validation_regex: Optional[str] = None
    options: Optional[List[str]] = None

class FormRequest(BaseModel):
    form_id: str
    task_id: str
    title: str
    description: str = ""
    fields: List[FormField]
    timeout: Optional[int] = 300  # 5 minutes

class FormResponse(BaseModel):
    form_id: str
    task_id: str
    data: Dict[str, str]
    submitted_at: datetime = Field(default_factory=datetime.utcnow)

# SSH and system models
class SSHConnection(BaseModel):
    hostname: str
    port: int = 22
    username: str
    password: Optional[str] = None
    private_key_path: Optional[str] = None
    timeout: int = 30

class CommandExecution(BaseModel):
    command: str
    working_directory: Optional[str] = None
    timeout: Optional[int] = None
    environment: Optional[Dict[str, str]] = None

class CommandResult(BaseModel):
    command: str
    exit_code: int
    stdout: str
    stderr: str
    execution_time: float
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# User context model
class UserContext(BaseModel):
    user_id: str
    permissions: List[str] = Field(default_factory=list)
    settings: Dict[str, Any] = Field(default_factory=dict)

# API Response models
class APIResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Any] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# Health check models
class HealthCheck(BaseModel):
    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    uptime: float
    active_connections: int
    active_tasks: int
    system_info: Optional[Dict[str, Any]] = None
